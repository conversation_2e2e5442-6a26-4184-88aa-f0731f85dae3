using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using PanelApi.Attributes;

namespace PanelApi.Controllers;

[ApiController]
[Route("api/[controller]")]
[EnableCors("AllowFrontend")]
[Authorize]
public class CompanyInfoController : ControllerBase
{
    private readonly ICompanyInfoService _companyInfoService;

    public CompanyInfoController(ICompanyInfoService companyInfoService)
    {
        _companyInfoService = companyInfoService;
    }

    [HttpGet]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<List<CompanyInfoListDto>>> GetList([FromQuery] int? page, [FromQuery] int? pageSize)
    {
        try
        {
            var companies = await _companyInfoService.GetListAsync(page, pageSize);
            return Ok(companies);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("{id}")]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<CompanyInfoDto>> GetById(Guid id)
    {
        try
        {
            var company = await _companyInfoService.GetByIdAsync(id);
            if (company == null)
                return NotFound(new { message = "Şirket bilgisi bulunamadı" });

            return Ok(company);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("active")]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<CompanyInfoDto>> GetActiveCompany()
    {
        try
        {
            var company = await _companyInfoService.GetActiveCompanyInfoAsync();
            if (company == null)
                return NotFound(new { message = "Aktif şirket bilgisi bulunamadı" });

            return Ok(company);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost]
    [RequirePermission("settings", "create")]
    public async Task<ActionResult> Create([FromBody] CompanyInfoCreateDto dto)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        try
        {
            var companyId = await _companyInfoService.CreateAsync(dto);
            return Ok(new { message = "Şirket bilgisi başarıyla oluşturuldu.", companyId = companyId });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPut("{id}")]
    [RequirePermission("settings", "update")]
    public async Task<ActionResult> Update(Guid id, [FromBody] CompanyInfoUpdateDto dto)
    {
        if (id != dto.Id)
            return BadRequest("ID uyuşmazlığı");

        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        try
        {
            await _companyInfoService.UpdateAsync(dto);
            return Ok(new { message = "Şirket bilgisi başarıyla güncellendi." });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpDelete("{id}")]
    [RequirePermission("settings", "delete")]
    public async Task<ActionResult> Delete(Guid id)
    {
        try
        {
            await _companyInfoService.DeleteAsync(id);
            return Ok(new { message = "Şirket bilgisi başarıyla silindi." });
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(new { message = ex.Message });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/activate")]
    [RequirePermission("settings", "update")]
    public async Task<ActionResult> Activate(Guid id)
    {
        try
        {
            var result = await _companyInfoService.ActivateAsync(id);
            if (!result)
                return NotFound(new { message = "Şirket bilgisi bulunamadı" });

            return Ok(new { message = "Şirket bilgisi başarıyla aktifleştirildi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/deactivate")]
    [RequirePermission("settings", "update")]
    public async Task<ActionResult> Deactivate(Guid id)
    {
        try
        {
            var result = await _companyInfoService.DeactivateAsync(id);
            if (!result)
                return NotFound(new { message = "Şirket bilgisi bulunamadı" });

            return Ok(new { message = "Şirket bilgisi başarıyla deaktifleştirildi." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("{id}/set-active")]
    [RequirePermission("settings", "update")]
    public async Task<ActionResult> SetAsActiveCompany(Guid id)
    {
        try
        {
            var result = await _companyInfoService.SetAsActiveCompanyAsync(id);
            if (!result)
                return NotFound(new { message = "Şirket bilgisi bulunamadı" });

            return Ok(new { message = "Şirket aktif şirket olarak ayarlandı." });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("search")]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<List<CompanyInfoListDto>>> Search([FromQuery] string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return BadRequest(new { message = "Arama terimi boş olamaz" });

        try
        {
            var companies = await _companyInfoService.SearchAsync(searchTerm);
            return Ok(companies);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("check-company-name")]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<bool>> CheckCompanyNameExists([FromQuery] string companyName, [FromQuery] Guid? excludeId = null)
    {
        if (string.IsNullOrWhiteSpace(companyName))
            return BadRequest(new { message = "Şirket adı boş olamaz" });

        try
        {
            var exists = await _companyInfoService.IsCompanyNameExistsAsync(companyName, excludeId);
            return Ok(new { exists = exists });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("check-email")]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<bool>> CheckEmailExists([FromQuery] string email, [FromQuery] Guid? excludeId = null)
    {
        if (string.IsNullOrWhiteSpace(email))
            return BadRequest(new { message = "Email boş olamaz" });

        try
        {
            var exists = await _companyInfoService.IsEmailExistsAsync(email, excludeId);
            return Ok(new { exists = exists });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("check-tax-number")]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult<bool>> CheckTaxNumberExists([FromQuery] string taxNumber, [FromQuery] Guid? excludeId = null)
    {
        if (string.IsNullOrWhiteSpace(taxNumber))
            return BadRequest(new { message = "Vergi numarası boş olamaz" });

        try
        {
            var exists = await _companyInfoService.IsTaxNumberExistsAsync(taxNumber, excludeId);
            return Ok(new { exists = exists });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("statistics")]
    [RequirePermission("settings", "read")]
    public async Task<ActionResult> GetStatistics()
    {
        try
        {
            var totalCompanies = await _companyInfoService.GetTotalCompaniesAsync();
            var hasActiveCompany = await _companyInfoService.HasActiveCompanyAsync();

            return Ok(new { 
                totalCompanies = totalCompanies,
                hasActiveCompany = hasActiveCompany
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }
}
