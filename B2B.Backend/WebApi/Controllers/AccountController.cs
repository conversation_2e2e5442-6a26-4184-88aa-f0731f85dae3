using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;
[ApiController]
[Route("/api/[controller]")]
[EnableCors("AllowFrontend")]
[Authorize]
public class AccountController : ControllerBase
{
    private readonly IAddressService _addressService;

    public AccountController(IAddressService addressService)
    {
        _addressService = addressService;
    }
    [HttpGet("{customerId}/addresses")]
    public async Task<List<AddressListDto>> GetAddressesAsync(Guid customerId)
    {
        try
        {
            return await _addressService.GetCustomerAddressesAsync(customerId);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            return [];
        }
    }
    [HttpPost("{customerId}/addresses")]
    public async Task<Guid> CreateAddressAsync(Guid customerId, AddressCreateDto dto)
    {
        try
        {
            return await _addressService.CreateCustomerAddressAsync(customerId, dto);
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            return Guid.Empty;
        }
    }

    [HttpPut("addresses")]
    public async Task<bool> UpdateAddressAsync(AddressUpdateDto dto)
    {
        try
        {
            await _addressService.UpdateAsync(dto);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            return false;
        }
    }
    [HttpDelete("addresses/{addressId}")]
    public async Task<bool> DeleteAddressAsync(Guid addressId)
    {
        try
        {
            await _addressService.DeleteAsync(addressId);
            return true;
        }
        catch (Exception ex)
        {
            Console.WriteLine(ex);
            return false;
        }
    }
}
