using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("api/customer")]
[EnableCors("AllowCustomerFrontend")]
public class CustomerAuthController : ControllerBase
{
    private readonly ICustomerAuthService _customerAuthService;

    public CustomerAuthController(ICustomerAuthService customerAuthService)
    {
        _customerAuthService = customerAuthService;
    }

    /// <summary>
    /// Customer login endpoint
    /// </summary>
    /// <param name="dto">Login credentials</param>
    /// <returns>JWT token and customer information</returns>
    [AllowAnonymous]
    [HttpPost("login")]
    public async Task<ActionResult<ApiResponse<CustomerAuthResponseDto>>> Login([FromBody] CustomerLoginDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<CustomerAuthResponseDto>.ErrorResponse(
                "Geçersiz veri.", 400,
                string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))));
        }

        try
        {
            var result = await _customerAuthService.LoginAsync(dto);

            if (result.IsSuccessful)
            {
                return Ok(ApiResponse<CustomerAuthResponseDto>.SuccessResponse(result, "Giriş başarılı"));
            }

            return Unauthorized(ApiResponse<CustomerAuthResponseDto>.ErrorResponse(result.Message, 401));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerAuthResponseDto>.ErrorResponse(
                "Giriş sırasında bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Customer registration endpoint
    /// </summary>
    /// <param name="dto">Registration data</param>
    /// <returns>JWT token and customer information</returns>
    [HttpPost("register")]
    public async Task<ActionResult<ApiResponse<CustomerAuthResponseDto>>> Register([FromBody] CustomerRegisterDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ApiResponse<CustomerAuthResponseDto>.ErrorResponse(
                "Geçersiz veri.", 400,
                string.Join(", ", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage))));
        }

        try
        {
            var result = await _customerAuthService.RegisterAsync(dto);

            if (result.IsSuccessful)
            {
                return Ok(ApiResponse<CustomerAuthResponseDto>.SuccessResponse(result, "Kayıt başarılı"));
            }

            return BadRequest(ApiResponse<CustomerAuthResponseDto>.ErrorResponse(result.Message, 400));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerAuthResponseDto>.ErrorResponse(
                "Kayıt sırasında bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Validate customer token endpoint
    /// </summary>
    /// <returns>Customer information if token is valid</returns>
    [HttpGet("validate")]
    public async Task<ActionResult<ApiResponse<CustomerDto>>> ValidateToken()
    {
        try
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return Unauthorized(ApiResponse<CustomerDto>.ErrorResponse("Token bulunamadı.", 401));
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            var customer = await _customerAuthService.ValidateTokenAsync(token);

            if (customer == null)
            {
                return Unauthorized(ApiResponse<CustomerDto>.ErrorResponse("Geçersiz token.", 401));
            }

            return Ok(ApiResponse<CustomerDto>.SuccessResponse(customer, "Token geçerli"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerDto>.ErrorResponse(
                "Token doğrulama sırasında bir hata oluştu.", 500, ex.Message));
        }
    }

    /// <summary>
    /// Get customer profile endpoint (requires authentication)
    /// </summary>
    /// <returns>Customer profile information</returns>
    [HttpGet("profile")]
    public async Task<ActionResult<ApiResponse<CustomerDto>>> GetProfile()
    {
        try
        {
            var authHeader = Request.Headers["Authorization"].FirstOrDefault();
            if (string.IsNullOrEmpty(authHeader) || !authHeader.StartsWith("Bearer "))
            {
                return Unauthorized(ApiResponse<CustomerDto>.ErrorResponse("Token bulunamadı.", 401));
            }

            var token = authHeader.Substring("Bearer ".Length).Trim();
            var customer = await _customerAuthService.ValidateTokenAsync(token);

            if (customer == null)
            {
                return Unauthorized(ApiResponse<CustomerDto>.ErrorResponse("Geçersiz token.", 401));
            }

            return Ok(ApiResponse<CustomerDto>.SuccessResponse(customer, "Profil bilgileri getirildi"));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<CustomerDto>.ErrorResponse(
                "Profil bilgileri getirilirken bir hata oluştu.", 500, ex.Message));
        }
    }
}
