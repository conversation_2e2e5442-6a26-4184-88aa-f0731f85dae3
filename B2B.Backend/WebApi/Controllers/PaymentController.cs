using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class PaymentController : ControllerBase
{
    private readonly IPaymentService _paymentService;
    private readonly IOrderService _orderService;

    public PaymentController(IPaymentService paymentService, IOrderService orderService)
    {
        _paymentService = paymentService;
        _orderService = orderService;
    }

    [HttpPost("iyzico/initialize")]
    public async Task<ActionResult> InitializeIyzicoPayment([FromBody] OrderPaymentRequest request)
    {
        try
        {
            // Sipariş bilgilerini doğrula
            var order = await _orderService.GetByIdAsync(request.OrderId);
            if (order == null) return NotFound(new { message = "Sipariş bulunamadı." });

            // İyzico için ödeme başlat
            var checkoutFormResult = await _paymentService.InitializeIyzicoCheckoutForm(order);

            return Ok(new
            {
                checkoutFormContent = checkoutFormResult.CheckoutFormContent,
                token = checkoutFormResult.Token,
                status = checkoutFormResult.Status
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpPost("iyzico/callback")]
    public async Task<ActionResult> IyzicoCallback([FromBody] IyzicoCallbackRequest request)
    {
        try
        {
            var result = await _paymentService.ValidateIyzicoPayment(request.Token);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }
}