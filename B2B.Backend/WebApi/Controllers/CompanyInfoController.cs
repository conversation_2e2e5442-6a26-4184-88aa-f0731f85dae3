using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class CompanyInfoController : ControllerBase
{
    private readonly ICompanyInfoService _companyInfoService;

    public CompanyInfoController(ICompanyInfoService companyInfoService)
    {
        _companyInfoService = companyInfoService;
    }

    [HttpGet("active")]
    public async Task<ActionResult<ApiResponse<CompanyInfoDto>>> GetActiveCompany()
    {
        try
        {
            var company = await _companyInfoService.GetActiveCompanyInfoAsync();
            if (company == null)
                return NotFound(ApiResponse<CompanyInfoDto>.NotFoundResponse("Aktif şirket bilgisi bulunamadı"));

            return Ok(ApiResponse<CompanyInfoDto>.SuccessResponse(company, "Şirket bilgisi başarıyla getirildi"));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<CompanyInfoDto>.BadRequestResponse(ex.Message));
        }
    }

    [HttpGet("{id}")]
    public async Task<ActionResult<CompanyInfoDto>> GetById(Guid id)
    {
        try
        {
            var company = await _companyInfoService.GetByIdAsync(id);
            if (company == null)
                return NotFound(new { message = "Şirket bilgisi bulunamadı" });

            return Ok(company);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("by-name/{companyName}")]
    public async Task<ActionResult<CompanyInfoDto>> GetByCompanyName(string companyName)
    {
        try
        {
            var company = await _companyInfoService.GetByCompanyNameAsync(companyName);
            if (company == null)
                return NotFound(new { message = "Şirket bilgisi bulunamadı" });

            return Ok(company);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }

    [HttpGet("search")]
    public async Task<ActionResult<List<CompanyInfoListDto>>> Search([FromQuery] string searchTerm)
    {
        if (string.IsNullOrWhiteSpace(searchTerm))
            return BadRequest(new { message = "Arama terimi boş olamaz" });

        try
        {
            var companies = await _companyInfoService.SearchAsync(searchTerm);
            return Ok(companies);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message });
        }
    }
}
