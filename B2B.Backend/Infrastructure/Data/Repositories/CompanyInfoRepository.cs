using Core.Entities;
using Core.Enums;
using Infrastructure.Data.Repositories.Interfaces;
using Infrastructure.Repositories;

namespace Infrastructure.Data.Repositories;

public class CompanyInfoRepository : GenericRepository<CompanyInfo>, ICompanyInfoRepository
{
    private readonly B2BDbContext _context;

    public CompanyInfoRepository(B2BDbContext context) : base(context)
    {
        _context = context;
    }

    public async Task AddToHistoryAsync(CompanyInfo companyInfo, ChangeType changeType, Guid employeeId)
    {
        var history = new CompanyInfoHistory
        {
            Id = Guid.CreateVersion7(),
            EntityId = companyInfo.Id,
            ChangeType = changeType,
            ChangeDate = DateTime.UtcNow,
            EmployeeId = employeeId,
            CreatedAt = companyInfo.CreatedAt,
            UpdatedAt = companyInfo.UpdatedAt,
            IsDeleted = companyInfo.IsDeleted,
            IsActive = companyInfo.IsActive,
            
            // Copy entity properties
            CompanyName = companyInfo.CompanyName,
            Description = companyInfo.Description,
            Address = companyInfo.Address,
            PhoneNumber = companyInfo.PhoneNumber,
            Email = companyInfo.Email,
            Website = companyInfo.Website,
            LogoUrl = companyInfo.LogoUrl,
            TaxNumber = companyInfo.TaxNumber,
            TaxOffice = companyInfo.TaxOffice,
            FacebookUrl = companyInfo.FacebookUrl,
            InstagramUrl = companyInfo.InstagramUrl,
            TwitterUrl = companyInfo.TwitterUrl,
            LinkedInUrl = companyInfo.LinkedInUrl,
            YoutubeUrl = companyInfo.YoutubeUrl,
            WhatsappNumber = companyInfo.WhatsappNumber,
            SupportEmail = companyInfo.SupportEmail,
            SalesEmail = companyInfo.SalesEmail,
            WorkingHours = companyInfo.WorkingHours,
            AboutUs = companyInfo.AboutUs,
            Mission = companyInfo.Mission,
            Vision = companyInfo.Vision,
            Latitude = companyInfo.Latitude,
            Longitude = companyInfo.Longitude,
            MetaTitle = companyInfo.MetaTitle,
            MetaDescription = companyInfo.MetaDescription,
            MetaKeywords = companyInfo.MetaKeywords
        };

        await _context.CompanyInfoHistory.AddAsync(history);
    }
}
