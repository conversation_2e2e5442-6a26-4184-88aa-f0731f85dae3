using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Infrastructure.Data.Repositories.Interfaces;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Infrastructure.Services;

public class CompanyInfoService : ICompanyInfoService
{
    private readonly ICompanyInfoRepository _companyInfoRepository;
    private readonly ILogger<CompanyInfoService> _logger;

    public CompanyInfoService(
        ICompanyInfoRepository companyInfoRepository,
        ILogger<CompanyInfoService> logger)
    {
        _companyInfoRepository = companyInfoRepository;
        _logger = logger;
    }

    public async Task<List<CompanyInfoListDto>> GetListAsync(int? page = null, int? pageSize = null)
    {
        try
        {
            var companies = await _companyInfoRepository.GetPagedAsync(page, pageSize);
            var result = new List<CompanyInfoListDto>();

            foreach (var company in companies.Where(c => !c.IsDeleted))
            {
                result.Add(new CompanyInfoListDto
                {
                    Id = company.Id,
                    CompanyName = company.CompanyName,
                    Email = company.Email,
                    PhoneNumber = company.PhoneNumber,
                    Website = company.Website,
                    IsActive = company.IsActive,
                    UpdatedAt = company.UpdatedAt
                });
            }

            return result.OrderByDescending(c => c.UpdatedAt).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting company info list");
            throw;
        }
    }

    public async Task<CompanyInfoDto?> GetByIdAsync(Guid id)
    {
        try
        {
            var company = await _companyInfoRepository.GetByIdAsync(id);
            if (company == null || company.IsDeleted)
                return null;

            return MapToDto(company);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting company info by id: {Id}", id);
            throw;
        }
    }

    public async Task<CompanyInfoDto?> GetActiveCompanyInfoAsync()
    {
        try
        {
            var company = await _companyInfoRepository.Query()
                .Where(c => c.IsActive && !c.IsDeleted)
                .FirstOrDefaultAsync();

            return company != null ? MapToDto(company) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting active company info");
            throw;
        }
    }

    public async Task<Guid> CreateAsync(CompanyInfoCreateDto dto)
    {
        try
        {
            // Check for existing company name
            if (await IsCompanyNameExistsAsync(dto.CompanyName))
                throw new InvalidOperationException("Bu şirket adı zaten kullanılıyor.");

            // Check for existing email
            if (!string.IsNullOrEmpty(dto.Email) && await IsEmailExistsAsync(dto.Email))
                throw new InvalidOperationException("Bu email adresi zaten kullanılıyor.");

            // Check for existing tax number
            if (!string.IsNullOrEmpty(dto.TaxNumber) && await IsTaxNumberExistsAsync(dto.TaxNumber))
                throw new InvalidOperationException("Bu vergi numarası zaten kullanılıyor.");

            var company = new CompanyInfo
            {
                Id = Guid.CreateVersion7(),
                CompanyName = dto.CompanyName,
                Description = dto.Description,
                Address = dto.Address,
                PhoneNumber = dto.PhoneNumber,
                Email = dto.Email,
                Website = dto.Website,
                LogoUrl = dto.LogoUrl,
                TaxNumber = dto.TaxNumber,
                TaxOffice = dto.TaxOffice,
                FacebookUrl = dto.FacebookUrl,
                InstagramUrl = dto.InstagramUrl,
                TwitterUrl = dto.TwitterUrl,
                LinkedInUrl = dto.LinkedInUrl,
                YoutubeUrl = dto.YoutubeUrl,
                WhatsappNumber = dto.WhatsappNumber,
                SupportEmail = dto.SupportEmail,
                SalesEmail = dto.SalesEmail,
                WorkingHours = dto.WorkingHours,
                AboutUs = dto.AboutUs,
                Mission = dto.Mission,
                Vision = dto.Vision,
                Latitude = dto.Latitude,
                Longitude = dto.Longitude,
                MetaTitle = dto.MetaTitle,
                MetaDescription = dto.MetaDescription,
                MetaKeywords = dto.MetaKeywords,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _companyInfoRepository.AddAsync(company);
            await _companyInfoRepository.SaveChangesAsync();

            // Add to history
            await _companyInfoRepository.AddToHistoryAsync(company, ChangeType.Created, Guid.Empty);
            await _companyInfoRepository.SaveChangesAsync();

            _logger.LogInformation("Company info created: {CompanyId} - {CompanyName}", company.Id, company.CompanyName);

            return company.Id;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating company info");
            throw;
        }
    }

    public async Task UpdateAsync(CompanyInfoUpdateDto dto)
    {
        try
        {
            var company = await _companyInfoRepository.GetByIdAsync(dto.Id);
            if (company == null || company.IsDeleted)
                throw new InvalidOperationException("Şirket bilgisi bulunamadı.");

            // Check for existing company name (exclude current)
            if (await IsCompanyNameExistsAsync(dto.CompanyName, dto.Id))
                throw new InvalidOperationException("Bu şirket adı zaten kullanılıyor.");

            // Check for existing email (exclude current)
            if (!string.IsNullOrEmpty(dto.Email) && await IsEmailExistsAsync(dto.Email, dto.Id))
                throw new InvalidOperationException("Bu email adresi zaten kullanılıyor.");

            // Check for existing tax number (exclude current)
            if (!string.IsNullOrEmpty(dto.TaxNumber) && await IsTaxNumberExistsAsync(dto.TaxNumber, dto.Id))
                throw new InvalidOperationException("Bu vergi numarası zaten kullanılıyor.");

            // Update company properties
            company.CompanyName = dto.CompanyName;
            company.Description = dto.Description;
            company.Address = dto.Address;
            company.PhoneNumber = dto.PhoneNumber;
            company.Email = dto.Email;
            company.Website = dto.Website;
            company.LogoUrl = dto.LogoUrl;
            company.TaxNumber = dto.TaxNumber;
            company.TaxOffice = dto.TaxOffice;
            company.FacebookUrl = dto.FacebookUrl;
            company.InstagramUrl = dto.InstagramUrl;
            company.TwitterUrl = dto.TwitterUrl;
            company.LinkedInUrl = dto.LinkedInUrl;
            company.YoutubeUrl = dto.YoutubeUrl;
            company.WhatsappNumber = dto.WhatsappNumber;
            company.SupportEmail = dto.SupportEmail;
            company.SalesEmail = dto.SalesEmail;
            company.WorkingHours = dto.WorkingHours;
            company.AboutUs = dto.AboutUs;
            company.Mission = dto.Mission;
            company.Vision = dto.Vision;
            company.Latitude = dto.Latitude;
            company.Longitude = dto.Longitude;
            company.MetaTitle = dto.MetaTitle;
            company.MetaDescription = dto.MetaDescription;
            company.MetaKeywords = dto.MetaKeywords;
            company.UpdatedAt = DateTime.UtcNow;

            _companyInfoRepository.Update(company);
            await _companyInfoRepository.SaveChangesAsync();

            // Add to history
            await _companyInfoRepository.AddToHistoryAsync(company, ChangeType.Updated, Guid.Empty);
            await _companyInfoRepository.SaveChangesAsync();

            _logger.LogInformation("Company info updated: {CompanyId} - {CompanyName}", company.Id, company.CompanyName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating company info: {Id}", dto.Id);
            throw;
        }
    }

    public async Task DeleteAsync(Guid id)
    {
        try
        {
            var company = await _companyInfoRepository.GetByIdAsync(id);
            if (company == null || company.IsDeleted)
                throw new InvalidOperationException("Şirket bilgisi bulunamadı.");

            company.IsDeleted = true;
            company.UpdatedAt = DateTime.UtcNow;

            _companyInfoRepository.Update(company);
            await _companyInfoRepository.SaveChangesAsync();

            // Add to history
            await _companyInfoRepository.AddToHistoryAsync(company, ChangeType.Deleted, Guid.Empty);
            await _companyInfoRepository.SaveChangesAsync();

            _logger.LogInformation("Company info deleted: {CompanyId} - {CompanyName}", company.Id, company.CompanyName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting company info: {Id}", id);
            throw;
        }
    }

    public async Task<bool> ActivateAsync(Guid id)
    {
        try
        {
            var company = await _companyInfoRepository.GetByIdAsync(id);
            if (company == null || company.IsDeleted)
                return false;

            company.IsActive = true;
            company.UpdatedAt = DateTime.UtcNow;

            _companyInfoRepository.Update(company);
            await _companyInfoRepository.SaveChangesAsync();

            // Add to history
            await _companyInfoRepository.AddToHistoryAsync(company, ChangeType.Updated, Guid.Empty);
            await _companyInfoRepository.SaveChangesAsync();

            _logger.LogInformation("Company info activated: {CompanyId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error activating company info: {Id}", id);
            return false;
        }
    }

    public async Task<bool> DeactivateAsync(Guid id)
    {
        try
        {
            var company = await _companyInfoRepository.GetByIdAsync(id);
            if (company == null || company.IsDeleted)
                return false;

            company.IsActive = false;
            company.UpdatedAt = DateTime.UtcNow;

            _companyInfoRepository.Update(company);
            await _companyInfoRepository.SaveChangesAsync();

            // Add to history
            await _companyInfoRepository.AddToHistoryAsync(company, ChangeType.Updated, Guid.Empty);
            await _companyInfoRepository.SaveChangesAsync();

            _logger.LogInformation("Company info deactivated: {CompanyId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deactivating company info: {Id}", id);
            return false;
        }
    }

    public async Task<bool> SetAsActiveCompanyAsync(Guid id)
    {
        try
        {
            // First deactivate all companies
            var allCompanies = await _companyInfoRepository.Query()
                .Where(c => !c.IsDeleted)
                .ToListAsync();

            foreach (var company in allCompanies)
            {
                company.IsActive = false;
                company.UpdatedAt = DateTime.UtcNow;
            }

            // Then activate the selected company
            var targetCompany = allCompanies.FirstOrDefault(c => c.Id == id);
            if (targetCompany == null)
                return false;

            targetCompany.IsActive = true;
            targetCompany.UpdatedAt = DateTime.UtcNow;

            _companyInfoRepository.UpdateRange(allCompanies);
            await _companyInfoRepository.SaveChangesAsync();

            _logger.LogInformation("Company set as active: {CompanyId}", id);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error setting company as active: {Id}", id);
            return false;
        }
    }

    public async Task<List<CompanyInfoListDto>> SearchAsync(string searchTerm)
    {
        try
        {
            var companies = await _companyInfoRepository.Query()
                .Where(c => !c.IsDeleted &&
                    (c.CompanyName.Contains(searchTerm) ||
                     (c.Email != null && c.Email.Contains(searchTerm)) ||
                     (c.PhoneNumber != null && c.PhoneNumber.Contains(searchTerm))))
                .ToListAsync();

            var result = new List<CompanyInfoListDto>();
            foreach (var company in companies)
            {
                result.Add(new CompanyInfoListDto
                {
                    Id = company.Id,
                    CompanyName = company.CompanyName,
                    Email = company.Email,
                    PhoneNumber = company.PhoneNumber,
                    Website = company.Website,
                    IsActive = company.IsActive,
                    UpdatedAt = company.UpdatedAt
                });
            }

            return result.OrderByDescending(c => c.UpdatedAt).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching company info: {SearchTerm}", searchTerm);
            throw;
        }
    }

    public async Task<CompanyInfoDto?> GetByCompanyNameAsync(string companyName)
    {
        try
        {
            var company = await _companyInfoRepository.Query()
                .Where(c => c.CompanyName == companyName && !c.IsDeleted)
                .FirstOrDefaultAsync();

            return company != null ? MapToDto(company) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting company info by name: {CompanyName}", companyName);
            throw;
        }
    }

    public async Task<CompanyInfoDto?> GetByEmailAsync(string email)
    {
        try
        {
            var company = await _companyInfoRepository.Query()
                .Where(c => c.Email == email && !c.IsDeleted)
                .FirstOrDefaultAsync();

            return company != null ? MapToDto(company) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting company info by email: {Email}", email);
            throw;
        }
    }

    public async Task<CompanyInfoDto?> GetByTaxNumberAsync(string taxNumber)
    {
        try
        {
            var company = await _companyInfoRepository.Query()
                .Where(c => c.TaxNumber == taxNumber && !c.IsDeleted)
                .FirstOrDefaultAsync();

            return company != null ? MapToDto(company) : null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting company info by tax number: {TaxNumber}", taxNumber);
            throw;
        }
    }

    public async Task<bool> IsCompanyNameExistsAsync(string companyName, Guid? excludeId = null)
    {
        try
        {
            var query = _companyInfoRepository.Query()
                .Where(c => c.CompanyName == companyName && !c.IsDeleted);

            if (excludeId.HasValue)
                query = query.Where(c => c.Id != excludeId.Value);

            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking company name exists: {CompanyName}", companyName);
            throw;
        }
    }

    public async Task<bool> IsEmailExistsAsync(string email, Guid? excludeId = null)
    {
        try
        {
            var query = _companyInfoRepository.Query()
                .Where(c => c.Email == email && !c.IsDeleted);

            if (excludeId.HasValue)
                query = query.Where(c => c.Id != excludeId.Value);

            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking email exists: {Email}", email);
            throw;
        }
    }

    public async Task<bool> IsTaxNumberExistsAsync(string taxNumber, Guid? excludeId = null)
    {
        try
        {
            var query = _companyInfoRepository.Query()
                .Where(c => c.TaxNumber == taxNumber && !c.IsDeleted);

            if (excludeId.HasValue)
                query = query.Where(c => c.Id != excludeId.Value);

            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking tax number exists: {TaxNumber}", taxNumber);
            throw;
        }
    }

    public async Task<bool> HasActiveCompanyAsync()
    {
        try
        {
            return await _companyInfoRepository.Query()
                .Where(c => c.IsActive && !c.IsDeleted)
                .AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if has active company");
            throw;
        }
    }

    public async Task<int> GetTotalCompaniesAsync()
    {
        try
        {
            return await _companyInfoRepository.Query()
                .Where(c => !c.IsDeleted)
                .CountAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting total companies count");
            throw;
        }
    }

    private static CompanyInfoDto MapToDto(CompanyInfo company)
    {
        return new CompanyInfoDto
        {
            Id = company.Id,
            CompanyName = company.CompanyName,
            Description = company.Description,
            Address = company.Address,
            PhoneNumber = company.PhoneNumber,
            Email = company.Email,
            Website = company.Website,
            LogoUrl = company.LogoUrl,
            TaxNumber = company.TaxNumber,
            TaxOffice = company.TaxOffice,
            FacebookUrl = company.FacebookUrl,
            InstagramUrl = company.InstagramUrl,
            TwitterUrl = company.TwitterUrl,
            LinkedInUrl = company.LinkedInUrl,
            YoutubeUrl = company.YoutubeUrl,
            WhatsappNumber = company.WhatsappNumber,
            SupportEmail = company.SupportEmail,
            SalesEmail = company.SalesEmail,
            WorkingHours = company.WorkingHours,
            AboutUs = company.AboutUs,
            Mission = company.Mission,
            Vision = company.Vision,
            Latitude = company.Latitude,
            Longitude = company.Longitude,
            MetaTitle = company.MetaTitle,
            MetaDescription = company.MetaDescription,
            MetaKeywords = company.MetaKeywords,
            IsActive = company.IsActive,
            CreatedAt = company.CreatedAt,
            UpdatedAt = company.UpdatedAt
        };
    }
}
