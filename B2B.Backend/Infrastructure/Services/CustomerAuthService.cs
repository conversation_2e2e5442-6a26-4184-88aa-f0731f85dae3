using Application.Contracts.DTOs;
using Application.Contracts.Interfaces;
using Core.Entities;
using Core.Enums;
using Core.Interfaces;
using Infrastructure.Helpers;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using System.IdentityModel.Tokens.Jwt;

namespace Infrastructure.Services;

public class CustomerAuthService : ICustomerAuthService
{
    private readonly ICustomerRepository _customerRepository;
    private readonly IEncryptionService _encryptionService;
    private readonly IPasswordHasher<Customer> _passwordHasher;
    private readonly IConfiguration _config;

    public CustomerAuthService(
        ICustomerRepository customerRepository,
        IEncryptionService encryptionService,
        IPasswordHasher<Customer> passwordHasher,
        IConfiguration config)
    {
        _customerRepository = customerRepository;
        _encryptionService = encryptionService;
        _passwordHasher = passwordHasher;
        _config = config;
    }

    public async Task<CustomerAuthResponseDto> LoginAsync(CustomerLoginDto dto)
    {
        try
        {
            // Email'i şifrele ve customer'ı bul
            var encryptedEmail = _encryptionService.GenerateLookupHash(dto.Email);
            var customer = await _customerRepository.FindByEmailHashAsync(encryptedEmail);

            if (customer == null || customer.IsDeleted)
            {
                return new CustomerAuthResponseDto
                {
                    IsSuccessful = false,
                    Message = "Email veya şifre hatalı."
                };
            }

            if (!customer.IsActive)
            {
                return new CustomerAuthResponseDto
                {
                    IsSuccessful = false,
                    Message = "Hesabınız aktif değil. Lütfen yönetici ile iletişime geçin."
                };
            }

            var decryptedEmail = _encryptionService.Decrypt(customer.Email);

            if (!decryptedEmail.Equals(dto.Email, StringComparison.OrdinalIgnoreCase))
            {
                // Bu durumun yaşanması çok nadirdir ve bir güvenlik anomalisidir. Loglanmalıdır.
                return new CustomerAuthResponseDto { IsSuccessful = false, Message = "Email veya şifre decrypt hatalı." };
            }
            // Şifre doğrulama
            var passwordResult = _passwordHasher.VerifyHashedPassword(customer, customer.Password, dto.Password);
            if (passwordResult != PasswordVerificationResult.Success &&
                passwordResult != PasswordVerificationResult.SuccessRehashNeeded)
            {
                return new CustomerAuthResponseDto
                {
                    IsSuccessful = false,
                    Message = "Email veya şifre hatalı."
                };
            }

            // JWT token oluştur
            var customerName = _encryptionService.DecryptIfNotEmpty(customer.NameSurname) ?? "";
            var customerEmail = _encryptionService.DecryptIfNotEmpty(customer.Email) ?? "";

            var token = JwtHelpers.GenerateCustomerJwt(
                customer,
                customerName,
                customerEmail,
                validIssuer: _config["NextAuth:Url"]!,
                secretKey: _config["NextAuth:Secret"]!);

            // Customer DTO oluştur
            var customerDto = await MapToCustomerDto(customer);

            return new CustomerAuthResponseDto
            {
                IsSuccessful = true,
                Message = "Giriş başarılı.",
                Token = token,
                Customer = customerDto
            };
        }
        catch (Exception ex)
        {
            return new CustomerAuthResponseDto
            {
                IsSuccessful = false,
                Message = "Giriş sırasında bir hata oluştu."
            };
        }
    }

    public async Task<CustomerAuthResponseDto> RegisterAsync(CustomerRegisterDto dto)
    {
        try
        {
            // Email kontrolü
            var encryptedEmail = _encryptionService.GenerateLookupHash(dto.Email);
            if (await _customerRepository.IsEmailHashExistsAsync(encryptedEmail))
            {
                return new CustomerAuthResponseDto
                {
                    IsSuccessful = false,
                    Message = "Bu email adresi zaten kullanılıyor."
                };
            }

            // Telefon kontrolü (eğer verilmişse)
            if (!string.IsNullOrEmpty(dto.PhoneNumber))
            {
                var encryptedPhone = _encryptionService.GenerateLookupHash(dto.PhoneNumber);
                if (await _customerRepository.IsPhoneExistsAsync(encryptedPhone))
                {
                    return new CustomerAuthResponseDto
                    {
                        IsSuccessful = false,
                        Message = "Bu telefon numarası zaten kullanılıyor."
                    };
                }
            }

            // Customer oluştur
            var customer = new Customer
            {
                Id = Guid.CreateVersion7(),
                NameSurname = _encryptionService.Encrypt(dto.NameSurname),
                Email = _encryptionService.Encrypt(dto.Email),
                EmailHash = encryptedEmail,
                PhoneNumber = _encryptionService.EncryptIfNotEmpty(dto.PhoneNumber),
                TaxOrIdentityNumber = _encryptionService.EncryptIfNotEmpty(dto.TaxOrIdentityNumber),
                TaxOffice = dto.TaxOffice,
                IsActive = true, // Yeni kayıtlar otomatik aktif
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Şifre hash'le
            customer.Password = _passwordHasher.HashPassword(customer, dto.Password);

            // Cart oluştur
            customer.Cart = new Cart
            {
                Id = Guid.CreateVersion7(),
                CustomerId = customer.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // Veritabanına kaydet
            await _customerRepository.AddAsync(customer);
            await _customerRepository.SaveChangesAsync();

            // History'e ekle
            await _customerRepository.AddToHistoryAsync(customer, ChangeType.Created, Guid.Empty);
            await _customerRepository.SaveChangesAsync();

            // JWT token oluştur
            var token = JwtHelpers.GenerateCustomerJwt(
                customer,
                dto.NameSurname,
                dto.Email,
                validIssuer: _config["NextAuth:Url"]!,
                secretKey: _config["NextAuth:Secret"]!);

            // Customer DTO oluştur
            var customerDto = await MapToCustomerDto(customer);

            return new CustomerAuthResponseDto
            {
                IsSuccessful = true,
                Message = "Kayıt başarılı.",
                Token = token,
                Customer = customerDto
            };
        }
        catch (Exception ex)
        {
            return new CustomerAuthResponseDto
            {
                IsSuccessful = false,
                Message = "Kayıt sırasında bir hata oluştu."
            };
        }
    }

    public async Task<CustomerDto?> ValidateTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var jsonToken = tokenHandler.ReadJwtToken(token);

            var customerIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "sub")?.Value;
            if (string.IsNullOrEmpty(customerIdClaim) || !Guid.TryParse(customerIdClaim, out var customerId))
                return null;

            var customer = await _customerRepository.GetByIdAsync(customerId);
            if (customer == null || customer.IsDeleted || !customer.IsActive)
                return null;

            return await MapToCustomerDto(customer);
        }
        catch
        {
            return null;
        }
    }

    public async Task<CustomerDto?> GetCustomerByEmailAsync(string email)
    {
        var encryptedEmail = _encryptionService.Encrypt(email);
        var customer = await _customerRepository.GetByEmailAsync(encryptedEmail);
        return customer != null ? await MapToCustomerDto(customer) : null;
    }

    public async Task<bool> VerifyPasswordAsync(Guid customerId, string password)
    {
        var customer = await _customerRepository.GetByIdAsync(customerId);
        if (customer == null || customer.IsDeleted)
            return false;

        var result = _passwordHasher.VerifyHashedPassword(customer, customer.Password, password);
        return result == PasswordVerificationResult.Success || result == PasswordVerificationResult.SuccessRehashNeeded;
    }

    private async Task<CustomerDto> MapToCustomerDto(Customer customer)
    {
        return new CustomerDto
        {
            Id = customer.Id,
            NameSurname = _encryptionService.DecryptIfNotEmpty(customer.NameSurname) ?? "",
            Email = _encryptionService.DecryptIfNotEmpty(customer.Email),
            PhoneNumber = _encryptionService.DecryptIfNotEmpty(customer.PhoneNumber),
            TaxOrIdentityNumber = _encryptionService.DecryptIfNotEmpty(customer.TaxOrIdentityNumber),
            TaxOffice = customer.TaxOffice,
            IsActive = customer.IsActive,
            CreatedAt = customer.CreatedAt,
            UpdatedAt = customer.UpdatedAt,
            AddressCount = 0, // Bu bilgiler auth için gerekli değil
            OrderCount = 0,
            TotalOrderAmount = 0,
            PointBalance = 0,
            CouponCount = 0
        };
    }
}
