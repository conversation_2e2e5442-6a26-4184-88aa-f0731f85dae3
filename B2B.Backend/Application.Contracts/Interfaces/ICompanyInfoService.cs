using Application.Contracts.DTOs;

namespace Application.Contracts.Interfaces;

public interface ICompanyInfoService
{
    // Basic CRUD operations
    Task<List<CompanyInfoListDto>> GetListAsync(int? page = null, int? pageSize = null);
    Task<CompanyInfoDto?> GetByIdAsync(Guid id);
    Task<CompanyInfoDto?> GetActiveCompanyInfoAsync();
    Task<Guid> CreateAsync(CompanyInfoCreateDto dto);
    Task UpdateAsync(CompanyInfoUpdateDto dto);
    Task DeleteAsync(Guid id);

    // Status operations
    Task<bool> ActivateAsync(Guid id);
    Task<bool> DeactivateAsync(Guid id);
    Task<bool> SetAsActiveCompanyAsync(Guid id);

    // Search operations
    Task<List<CompanyInfoListDto>> SearchAsync(string searchTerm);
    Task<CompanyInfoDto?> GetByCompanyNameAsync(string companyName);
    Task<CompanyInfoDto?> GetByEmailAsync(string email);
    Task<CompanyInfoDto?> GetByTaxNumberAsync(string taxNumber);

    // Validation operations
    Task<bool> IsCompanyNameExistsAsync(string companyName, Guid? excludeId = null);
    Task<bool> IsEmailExistsAsync(string email, Guid? excludeId = null);
    Task<bool> IsTaxNumberExistsAsync(string taxNumber, Guid? excludeId = null);

    // Utility operations
    Task<bool> HasActiveCompanyAsync();
    Task<int> GetTotalCompaniesAsync();
}
