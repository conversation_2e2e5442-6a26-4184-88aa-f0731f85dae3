namespace Application.Contracts.DTOs;

/// <summary>
/// Generic API response wrapper for consistent response structure
/// </summary>
/// <typeparam name="T">Response data type</typeparam>
public class ApiResponse<T>
{
    /// <summary>
    /// Indicates if the operation was successful
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// Response message (success or error message)
    /// </summary>
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Response data (null if error)
    /// </summary>
    public T? Data { get; set; }

    /// <summary>
    /// Error details (null if success)
    /// </summary>
    public object? Error { get; set; }

    /// <summary>
    /// HTTP status code
    /// </summary>
    public int StatusCode { get; set; }

    /// <summary>
    /// Request timestamp
    /// </summary>
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Additional metadata (pagination, filters, etc.)
    /// </summary>
    public object? Metadata { get; set; }

    // Success response factory methods
    public static ApiResponse<T> SuccessResponse(T data, string message = "<PERSON><PERSON>lem başarılı", int statusCode = 200, object? metadata = null)
    {
        return new ApiResponse<T>
        {
            Success = true,
            Message = message,
            Data = data,
            StatusCode = statusCode,
            Metadata = metadata
        };
    }

    // Error response factory methods
    public static ApiResponse<T> ErrorResponse(string message, int statusCode = 500, object? error = null)
    {
        return new ApiResponse<T>
        {
            Success = false,
            Message = message,
            StatusCode = statusCode,
            Error = error
        };
    }

    public static ApiResponse<T> NotFoundResponse(string message = "Kayıt bulunamadı")
    {
        return ErrorResponse(message, 404);
    }

    public static ApiResponse<T> BadRequestResponse(string message = "Geçersiz istek")
    {
        return ErrorResponse(message, 400);
    }

    public static ApiResponse<T> UnauthorizedResponse(string message = "Yetkisiz erişim")
    {
        return ErrorResponse(message, 401);
    }

    public static ApiResponse<T> ForbiddenResponse(string message = "Erişim yasak")
    {
        return ErrorResponse(message, 403);
    }
}

/// <summary>
/// Non-generic API response for operations without return data
/// </summary>
public class ApiResponse : ApiResponse<object>
{
    // Success response factory methods
    public static ApiResponse SuccessResponse(string message = "İşlem başarılı", int statusCode = 200, object? metadata = null)
    {
        return new ApiResponse
        {
            Success = true,
            Message = message,
            StatusCode = statusCode,
            Metadata = metadata
        };
    }

    // Error response factory methods
    public new static ApiResponse ErrorResponse(string message, int statusCode = 500, object? error = null)
    {
        return new ApiResponse
        {
            Success = false,
            Message = message,
            StatusCode = statusCode,
            Error = error
        };
    }

    public new static ApiResponse NotFoundResponse(string message = "Kayıt bulunamadı")
    {
        return ErrorResponse(message, 404);
    }

    public new static ApiResponse BadRequestResponse(string message = "Geçersiz istek")
    {
        return ErrorResponse(message, 400);
    }

    public new static ApiResponse UnauthorizedResponse(string message = "Yetkisiz erişim")
    {
        return ErrorResponse(message, 401);
    }

    public new static ApiResponse ForbiddenResponse(string message = "Erişim yasak")
    {
        return ErrorResponse(message, 403);
    }
}

/// <summary>
/// Paginated API response for list operations
/// </summary>
/// <typeparam name="T">List item type</typeparam>
public class PaginatedApiResponse<T> : ApiResponse<List<T>>
{
    /// <summary>
    /// Pagination information
    /// </summary>
    public PaginationDto? Pagination { get; set; }

    /// <summary>
    /// Filter options (for product lists, etc.)
    /// </summary>
    public object? FilterOptions { get; set; }

    public static PaginatedApiResponse<T> SuccessResponse(
        List<T> data, 
        PaginationDto pagination, 
        string message = "İşlem başarılı", 
        object? filterOptions = null,
        object? metadata = null)
    {
        return new PaginatedApiResponse<T>
        {
            Success = true,
            Message = message,
            Data = data,
            Pagination = pagination,
            FilterOptions = filterOptions,
            StatusCode = 200,
            Metadata = metadata
        };
    }

    public new static PaginatedApiResponse<T> ErrorResponse(string message, int statusCode = 500, object? error = null)
    {
        return new PaginatedApiResponse<T>
        {
            Success = false,
            Message = message,
            StatusCode = statusCode,
            Error = error
        };
    }
}
