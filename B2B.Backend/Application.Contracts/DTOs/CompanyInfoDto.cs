using System.ComponentModel.DataAnnotations;

namespace Application.Contracts.DTOs;

public class CompanyInfoDto
{
    public Guid Id { get; set; }
    public string CompanyName { get; set; } = null!;
    public string? Description { get; set; }
    public string? Address { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }
    public string? Website { get; set; }
    public string? LogoUrl { get; set; }
    public string? TaxNumber { get; set; }
    public string? TaxOffice { get; set; }
    
    // Sosyal Medya Linkleri
    public string? FacebookUrl { get; set; }
    public string? InstagramUrl { get; set; }
    public string? TwitterUrl { get; set; }
    public string? LinkedInUrl { get; set; }
    public string? YoutubeUrl { get; set; }
    
    // İletişim Bilgileri
    public string? WhatsappNumber { get; set; }
    public string? SupportEmail { get; set; }
    public string? SalesEmail { get; set; }
    
    // Çalışma Saatleri
    public string? WorkingHours { get; set; }
    
    // Şirket Hakkında
    public string? AboutUs { get; set; }
    public string? Mission { get; set; }
    public string? Vision { get; set; }
    
    // Konum Bilgileri
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    
    // Meta Bilgiler
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public string? MetaKeywords { get; set; }
    
    public bool IsActive { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
}

public class CompanyInfoCreateDto
{
    [Required]
    [StringLength(200, MinimumLength = 2)]
    public string CompanyName { get; set; } = null!;

    [StringLength(1000)]
    public string? Description { get; set; }

    [StringLength(500)]
    public string? Address { get; set; }

    [Phone]
    [StringLength(100)]
    public string? PhoneNumber { get; set; }

    [EmailAddress]
    [StringLength(100)]
    public string? Email { get; set; }

    [Url]
    [StringLength(200)]
    public string? Website { get; set; }

    [StringLength(200)]
    public string? LogoUrl { get; set; }

    [StringLength(50)]
    public string? TaxNumber { get; set; }

    [StringLength(100)]
    public string? TaxOffice { get; set; }

    // Sosyal Medya Linkleri
    [Url]
    [StringLength(200)]
    public string? FacebookUrl { get; set; }

    [Url]
    [StringLength(200)]
    public string? InstagramUrl { get; set; }

    [Url]
    [StringLength(200)]
    public string? TwitterUrl { get; set; }

    [Url]
    [StringLength(200)]
    public string? LinkedInUrl { get; set; }

    [Url]
    [StringLength(200)]
    public string? YoutubeUrl { get; set; }

    // İletişim Bilgileri
    [Phone]
    [StringLength(100)]
    public string? WhatsappNumber { get; set; }

    [EmailAddress]
    [StringLength(100)]
    public string? SupportEmail { get; set; }

    [EmailAddress]
    [StringLength(100)]
    public string? SalesEmail { get; set; }

    // Çalışma Saatleri
    [StringLength(200)]
    public string? WorkingHours { get; set; }

    // Şirket Hakkında
    [StringLength(2000)]
    public string? AboutUs { get; set; }

    [StringLength(1000)]
    public string? Mission { get; set; }

    [StringLength(1000)]
    public string? Vision { get; set; }

    // Konum Bilgileri
    [Range(-90, 90)]
    public decimal? Latitude { get; set; }

    [Range(-180, 180)]
    public decimal? Longitude { get; set; }

    // Meta Bilgiler
    [StringLength(200)]
    public string? MetaTitle { get; set; }

    [StringLength(500)]
    public string? MetaDescription { get; set; }

    [StringLength(300)]
    public string? MetaKeywords { get; set; }
}

public class CompanyInfoUpdateDto
{
    [Required]
    public Guid Id { get; set; }

    [Required]
    [StringLength(200, MinimumLength = 2)]
    public string CompanyName { get; set; } = null!;

    [StringLength(1000)]
    public string? Description { get; set; }

    [StringLength(500)]
    public string? Address { get; set; }

    [Phone]
    [StringLength(100)]
    public string? PhoneNumber { get; set; }

    [EmailAddress]
    [StringLength(100)]
    public string? Email { get; set; }

    [Url]
    [StringLength(200)]
    public string? Website { get; set; }

    [StringLength(200)]
    public string? LogoUrl { get; set; }

    [StringLength(50)]
    public string? TaxNumber { get; set; }

    [StringLength(100)]
    public string? TaxOffice { get; set; }

    // Sosyal Medya Linkleri
    [Url]
    [StringLength(200)]
    public string? FacebookUrl { get; set; }

    [Url]
    [StringLength(200)]
    public string? InstagramUrl { get; set; }

    [Url]
    [StringLength(200)]
    public string? TwitterUrl { get; set; }

    [Url]
    [StringLength(200)]
    public string? LinkedInUrl { get; set; }

    [Url]
    [StringLength(200)]
    public string? YoutubeUrl { get; set; }

    // İletişim Bilgileri
    [Phone]
    [StringLength(100)]
    public string? WhatsappNumber { get; set; }

    [EmailAddress]
    [StringLength(100)]
    public string? SupportEmail { get; set; }

    [EmailAddress]
    [StringLength(100)]
    public string? SalesEmail { get; set; }

    // Çalışma Saatleri
    [StringLength(200)]
    public string? WorkingHours { get; set; }

    // Şirket Hakkında
    [StringLength(2000)]
    public string? AboutUs { get; set; }

    [StringLength(1000)]
    public string? Mission { get; set; }

    [StringLength(1000)]
    public string? Vision { get; set; }

    // Konum Bilgileri
    [Range(-90, 90)]
    public decimal? Latitude { get; set; }

    [Range(-180, 180)]
    public decimal? Longitude { get; set; }

    // Meta Bilgiler
    [StringLength(200)]
    public string? MetaTitle { get; set; }

    [StringLength(500)]
    public string? MetaDescription { get; set; }

    [StringLength(300)]
    public string? MetaKeywords { get; set; }
}

public class CompanyInfoListDto
{
    public Guid Id { get; set; }
    public string CompanyName { get; set; } = null!;
    public string? Email { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Website { get; set; }
    public bool IsActive { get; set; }
    public DateTime UpdatedAt { get; set; }
}
