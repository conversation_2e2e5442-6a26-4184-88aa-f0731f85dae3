using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Core.Entities;

[Table("CompanyInfo")]
public partial class CompanyInfo : BaseEntity
{
    [Required]
    [MaxLength(200)]
    public string CompanyName { get; set; } = null!;

    [MaxLength(1000)]
    public string? Description { get; set; }

    [MaxLength(500)]
    public string? Address { get; set; }

    [MaxLength(100)]
    public string? PhoneNumber { get; set; }

    [MaxLength(100)]
    public string? Email { get; set; }

    [MaxLength(200)]
    public string? Website { get; set; }

    [MaxLength(200)]
    public string? LogoUrl { get; set; }

    [MaxLength(50)]
    public string? TaxNumber { get; set; }

    [MaxLength(100)]
    public string? TaxOffice { get; set; }

    // Sosyal Medya Linkleri
    [MaxLength(200)]
    public string? FacebookUrl { get; set; }

    [MaxLength(200)]
    public string? InstagramUrl { get; set; }

    [MaxLength(200)]
    public string? TwitterUrl { get; set; }

    [MaxLength(200)]
    public string? LinkedInUrl { get; set; }

    [MaxLength(200)]
    public string? YoutubeUrl { get; set; }

    // İletişim Bilgileri
    [MaxLength(100)]
    public string? WhatsappNumber { get; set; }

    [MaxLength(100)]
    public string? SupportEmail { get; set; }

    [MaxLength(100)]
    public string? SalesEmail { get; set; }

    // Çalışma Saatleri
    [MaxLength(200)]
    public string? WorkingHours { get; set; }

    // Şirket Hakkında
    [MaxLength(2000)]
    public string? AboutUs { get; set; }

    [MaxLength(1000)]
    public string? Mission { get; set; }

    [MaxLength(1000)]
    public string? Vision { get; set; }

    // Konum Bilgileri
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }

    // Meta Bilgiler
    [MaxLength(200)]
    public string? MetaTitle { get; set; }

    [MaxLength(500)]
    public string? MetaDescription { get; set; }

    [MaxLength(300)]
    public string? MetaKeywords { get; set; }

    public static void Configure(EntityTypeBuilder<CompanyInfo> builder)
    {
        builder.HasIndex(c => c.CompanyName);
        builder.HasIndex(c => c.Email);
        builder.HasIndex(c => c.TaxNumber);

        // Decimal precision for coordinates
        builder.Property(c => c.Latitude)
            .HasPrecision(18, 6);
        
        builder.Property(c => c.Longitude)
            .HasPrecision(18, 6);
    }
}

[Table("CompanyInfoHistory")]
public class CompanyInfoHistory : HistoryBaseEntity
{
    // Entity properties
    public string CompanyName { get; set; } = null!;
    public string? Description { get; set; }
    public string? Address { get; set; }
    public string? PhoneNumber { get; set; }
    public string? Email { get; set; }
    public string? Website { get; set; }
    public string? LogoUrl { get; set; }
    public string? TaxNumber { get; set; }
    public string? TaxOffice { get; set; }
    public string? FacebookUrl { get; set; }
    public string? InstagramUrl { get; set; }
    public string? TwitterUrl { get; set; }
    public string? LinkedInUrl { get; set; }
    public string? YoutubeUrl { get; set; }
    public string? WhatsappNumber { get; set; }
    public string? SupportEmail { get; set; }
    public string? SalesEmail { get; set; }
    public string? WorkingHours { get; set; }
    public string? AboutUs { get; set; }
    public string? Mission { get; set; }
    public string? Vision { get; set; }
    public decimal? Latitude { get; set; }
    public decimal? Longitude { get; set; }
    public string? MetaTitle { get; set; }
    public string? MetaDescription { get; set; }
    public string? MetaKeywords { get; set; }
}
