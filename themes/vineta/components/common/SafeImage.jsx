"use client";
import React, { useState, useEffect } from 'react';
import { getImageSrc, DEFAULT_PLACEHOLDER } from '@/lib/utils/imageUtils';

/**
 * Safe Image component that handles loading errors gracefully
 * Falls back to placeholder when image fails to load
 * Uses regular img tag to avoid Next.js Image optimization issues with 404s
 */
export default function SafeImage({
  src,
  alt,
  width,
  height,
  className = '',
  placeholder = DEFAULT_PLACEHOLDER,
  style = {},
  ...props
}) {
  const [imageSrc, setImageSrc] = useState(placeholder);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const originalSrc = getImageSrc(src, placeholder);

    // If src is placeholder or empty, don't try to load
    if (!src || src === '' || originalSrc === placeholder) {
      setImageSrc(placeholder);
      setIsLoading(false);
      setHasError(false);
      return;
    }

    // Test if image exists before setting it
    const img = new Image();

    img.onload = () => {
      setImageSrc(originalSrc);
      setIsLoading(false);
      setHasError(false);
    };

    img.onerror = () => {
      setImageSrc(placeholder);
      setIsLoading(false);
      setHasError(true);
    };

    img.src = originalSrc;

    // Cleanup
    return () => {
      img.onload = null;
      img.onerror = null;
    };
  }, [src, placeholder]);

  const handleError = () => {
    if (!hasError && imageSrc !== placeholder) {
      setHasError(true);
      setImageSrc(placeholder);
    }
  };

  const handleLoad = () => {
    setIsLoading(false);
    setHasError(false);
  };

  return (
    <img
      src={imageSrc}
      alt={alt}
      width={width}
      height={height}
      className={className}
      style={{
        objectFit: 'cover',
        ...style
      }}
      onError={handleError}
      onLoad={handleLoad}
      loading="lazy"
      {...props}
    />
  );
}
