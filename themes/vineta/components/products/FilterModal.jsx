"use client";
import React from "react";
import Slider from "rc-slider";

export default function FilterModal({
  allProps,
  filterOptions = {
    categories: [],
    brands: [],
    variants: [],
    priceRange: { min: 0, max: 1000 }
  }
}) {
  return (
    <div
      className="offcanvas offcanvas-start canvas-sidebar canvas-filter"
      id="filterShop"
    >
      <div className="canvas-wrapper">
        <div className="canvas-header">
          <span className="title">Filtrele</span>
          <button
            className="icon-close icon-close-popup"
            data-bs-dismiss="offcanvas"
            aria-label="Kapat"
          />
        </div>
        <div className="canvas-body">
          {/* Kategoriler */}
          {filterOptions.categories && filterOptions.categories.length > 0 && (
            <div className="widget-facet">
              <div
                className="facet-title text-xl fw-medium"
                data-bs-target="#categories"
                role="button"
                data-bs-toggle="collapse"
                aria-expanded="true"
                aria-controls="categories"
              >
                <span>Kategoriler</span>
                <span className="icon icon-arrow-up" />
              </div>
              <div id="categories" className="collapse show">
                <ul className="list-categories current-scrollbar">
                  {filterOptions.categories.map((category) => (
                    <li key={category.id} className="cate-item">
                      <div
                        className={`text-sm link cursor-pointer ${allProps.selectedCategories?.includes(category.id) ? 'text-primary' : ''}`}
                        onClick={() => allProps.setSelectedCategories && allProps.setSelectedCategories(category.id)}
                      >
                        <span>{category.name}</span>
                        <span className="count">({category.productCount || 0})</span>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* Stok Durumu */}
          <div className="widget-facet">
            <div
              className="facet-title text-xl fw-medium"
              data-bs-target="#availability"
              role="button"
              data-bs-toggle="collapse"
              aria-expanded="true"
              aria-controls="availability"
            >
              <span>Stok Durumu</span>
              <span className="icon icon-arrow-up" />
            </div>
            <div id="availability" className="collapse show">
              <ul className="collapse-body filter-group-check current-scrollbar">
                <li
                  className="list-item"
                  onClick={() => allProps.setAvailability && allProps.setAvailability(true)}
                >
                  <input
                    type="radio"
                    name="availability"
                    className="tf-check"
                    id="inStock"
                    readOnly
                    checked={allProps.availability === true}
                  />
                  <label className="label">
                    <span>Stokta Var</span>&nbsp;
                    <span className="count">({filterOptions.stockCounts?.inStock || 0})</span>
                  </label>
                </li>
                <li
                  className="list-item"
                  onClick={() => allProps.setAvailability && allProps.setAvailability(false)}
                >
                  <input
                    type="radio"
                    name="availability"
                    className="tf-check"
                    id="outStock"
                    checked={allProps.availability === false}
                    readOnly
                  />
                  <label className="label">
                    <span>Stokta Yok</span>&nbsp;
                    <span className="count">({filterOptions.stockCounts?.outOfStock || 0})</span>
                  </label>
                </li>
              </ul>
            </div>
          </div>

          {/* Fiyat Aralığı */}
          <div className="widget-facet">
            <div
              className="facet-title text-xl fw-medium"
              data-bs-target="#price"
              role="button"
              data-bs-toggle="collapse"
              aria-expanded="true"
              aria-controls="price"
            >
              <span>Fiyat</span>
              <span className="icon icon-arrow-up" />
            </div>
            <div id="price" className="collapse show">
              <div className="collapse-body widget-price filter-price">
                <div
                  className="price-val-range"
                  id="price-value-range"
                  data-min={filterOptions.priceRange?.min || 0}
                  data-max={filterOptions.priceRange?.max || 1000}
                >
                  <Slider
                    value={allProps.price || [filterOptions.priceRange?.min || 0, filterOptions.priceRange?.max || 1000]}
                    onChange={(price) => allProps.setPrice && allProps.setPrice(price)}
                    range
                    min={filterOptions.priceRange?.min || 0}
                    max={filterOptions.priceRange?.max || 1000}
                  />
                </div>
                <div className="box-value-price">
                  <span className="text-sm">Fiyat:</span>
                  <div className="price-box">
                    <div
                      className="price-val"
                      id="price-min-value"
                      data-currency="₺"
                    >
                      {allProps.price ? allProps.price[0] : (filterOptions.priceRange?.min || 0)}₺
                    </div>
                    <span>-</span>
                    <div
                      className="price-val"
                      id="price-max-value"
                      data-currency="₺"
                    >
                      {allProps.price ? allProps.price[1] : (filterOptions.priceRange?.max || 1000)}₺
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Varyantlar (Renk, Beden vs.) */}
          {filterOptions.variants && filterOptions.variants.map((variant) => (
            <div key={variant.type} className="widget-facet">
              <div
                className="facet-title text-xl fw-medium"
                data-bs-target={`#${variant.type}`}
                role="button"
                data-bs-toggle="collapse"
                aria-expanded="true"
                aria-controls={variant.type}
              >
                <span>{variant.displayName}</span>
                <span className="icon icon-arrow-up" />
              </div>
              <div id={variant.type} className="collapse show">
                {variant.type === 'color' ? (
                  <div className="collapse-body filter-color-box flat-check-list">
                    {variant.values.map((value) => (
                      <div
                        key={value.id}
                        className={`check-item color-item color-check ${allProps.selectedVariants?.[variant.type]?.includes(value.id) ? "active" : ""
                          }`}
                        onClick={() => allProps.setSelectedVariants && allProps.setSelectedVariants(variant.type, value.id)}
                      >
                        <span
                          className={`color`}
                          style={{ backgroundColor: value.colorCode || '#ccc' }}
                        />
                        <span className="color-text">{value.name}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="collapse-body filter-size-box flat-check-list">
                    {variant.values.map((value) => (
                      <div
                        key={value.id}
                        onClick={() => allProps.setSelectedVariants && allProps.setSelectedVariants(variant.type, value.id)}
                        className={`check-item size-item size-check ${allProps.selectedVariants?.[variant.type]?.includes(value.id) ? "active" : ""
                          }`}
                      >
                        <span className="size">{value.name}</span>&nbsp;
                        <span className="count">({value.productCount || 0})</span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          ))}

          {/* Markalar */}
          {filterOptions.brands && filterOptions.brands.length > 0 && (
            <div className="widget-facet">
              <div
                className="facet-title text-xl fw-medium"
                data-bs-target="#brand"
                role="button"
                data-bs-toggle="collapse"
                aria-expanded="true"
                aria-controls="brand"
              >
                <span>Marka</span>
                <span className="icon icon-arrow-up" />
              </div>
              <div id="brand" className="collapse show">
                <ul className="collapse-body filter-group-check current-scrollbar">
                  {filterOptions.brands.map((brand) => (
                    <li
                      key={brand.id}
                      className="list-item"
                      onClick={() => allProps.setSelectedBrands && allProps.setSelectedBrands(brand.id)}
                    >
                      <input
                        type="checkbox"
                        className="tf-check"
                        readOnly
                        checked={allProps.selectedBrands?.includes(brand.id) || false}
                      />
                      <label className="label">
                        <span>{brand.name}</span>&nbsp;
                        <span className="count">({brand.productCount || 0})</span>
                      </label>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}

          {/* İndirimli Ürünler */}
          <div className="widget-facet">
            <div
              className="facet-title text-xl fw-medium"
              data-bs-target="#discount"
              role="button"
              data-bs-toggle="collapse"
              aria-expanded="true"
              aria-controls="discount"
            >
              <span>İndirim</span>
              <span className="icon icon-arrow-up" />
            </div>
            <div id="discount" className="collapse show">
              <ul className="collapse-body filter-group-check current-scrollbar">
                <li
                  className="list-item"
                  onClick={() => allProps.setOnDiscount && allProps.setOnDiscount(!allProps.onDiscount)}
                >
                  <input
                    type="checkbox"
                    className="tf-check"
                    readOnly
                    checked={allProps.onDiscount || false}
                  />
                  <label className="label">
                    <span>İndirimli Ürünler</span>&nbsp;
                    <span className="count">({filterOptions.discountCount || 0})</span>
                  </label>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}