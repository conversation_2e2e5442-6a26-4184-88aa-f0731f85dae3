import React from 'react';
import Head from 'next/head';

/**
 * Canonical URL Management Component
 * SEO için canonical URL yönetimi
 */

export default function CanonicalURL({ url, alternateUrls = [] }) {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com';
  const canonicalUrl = url?.startsWith('http') ? url : `${baseUrl}${url}`;

  return (
    <Head>
      {/* Canonical URL */}
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Alternate URLs (farklı diller için) */}
      {alternateUrls.map((alternate, index) => (
        <link
          key={index}
          rel="alternate"
          hrefLang={alternate.lang}
          href={alternate.url?.startsWith('http') ? alternate.url : `${baseUrl}${alternate.url}`}
        />
      ))}
      
      {/* Self-referencing hreflang */}
      <link rel="alternate" hrefLang="tr" href={canonicalUrl} />
      <link rel="alternate" hrefLang="x-default" href={canonicalUrl} />
    </Head>
  );
}

/**
 * Canonical URL utility fonksiyonları
 */

// URL'yi temizle ve normalize et
export const normalizeUrl = (url) => {
  if (!url) return '/';
  
  // Leading slash ekle
  if (!url.startsWith('/')) {
    url = '/' + url;
  }
  
  // Trailing slash kaldır (root hariç)
  if (url.length > 1 && url.endsWith('/')) {
    url = url.slice(0, -1);
  }
  
  // Query parametrelerini kaldır (SEO için)
  const urlObj = new URL(url, 'https://example.com');
  return urlObj.pathname;
};

// Sayfa türüne göre canonical URL oluştur
export const generateCanonicalUrl = (pageType, params = {}) => {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com';
  
  switch (pageType) {
    case 'product':
      return `${baseUrl}/products/${params.slug}`;
    
    case 'category':
      return `${baseUrl}/kategoriler/${params.slug}`;
    
    case 'products':
      return `${baseUrl}/urunler`;
    
    case 'new-season':
      return `${baseUrl}/yeni-sezon`;
    
    case 'discounted':
      return `${baseUrl}/indirim`;
    
    case 'home':
      return baseUrl;
    
    case 'search':
      return `${baseUrl}/products${params.query ? `?q=${encodeURIComponent(params.query)}` : ''}`;
    
    default:
      return `${baseUrl}${params.path || '/'}`;
  }
};

// Filter parametrelerini canonical URL'den çıkar
export const getCleanCanonicalUrl = (currentUrl) => {
  try {
    const url = new URL(currentUrl);
    
    // SEO için önemli olmayan parametreleri kaldır
    const paramsToRemove = [
      'page',
      'sort',
      'view',
      'utm_source',
      'utm_medium',
      'utm_campaign',
      'utm_content',
      'utm_term',
      'fbclid',
      'gclid',
    ];
    
    paramsToRemove.forEach(param => {
      url.searchParams.delete(param);
    });
    
    // Eğer hiç parametre kalmadıysa query string'i kaldır
    if (url.searchParams.toString() === '') {
      return url.pathname;
    }
    
    return url.pathname + '?' + url.searchParams.toString();
  } catch (error) {
    console.error('Error cleaning canonical URL:', error);
    return currentUrl;
  }
};

// Pagination için canonical URL yönetimi
export const getPaginationCanonicalUrl = (baseUrl, currentPage, totalPages) => {
  // İlk sayfa için canonical URL'de page parametresi olmamalı
  if (currentPage <= 1) {
    return getCleanCanonicalUrl(baseUrl);
  }
  
  // Diğer sayfalar için page parametresi ekle
  const url = new URL(baseUrl);
  url.searchParams.set('page', currentPage.toString());
  
  return url.pathname + '?' + url.searchParams.toString();
};

// Prev/Next link'leri için URL'ler
export const getPaginationLinks = (baseUrl, currentPage, totalPages) => {
  const links = {};
  
  // Previous page
  if (currentPage > 1) {
    if (currentPage === 2) {
      // İkinci sayfadan birinci sayfaya giderken page parametresi olmamalı
      links.prev = getCleanCanonicalUrl(baseUrl);
    } else {
      const prevUrl = new URL(baseUrl);
      prevUrl.searchParams.set('page', (currentPage - 1).toString());
      links.prev = prevUrl.pathname + '?' + prevUrl.searchParams.toString();
    }
  }
  
  // Next page
  if (currentPage < totalPages) {
    const nextUrl = new URL(baseUrl);
    nextUrl.searchParams.set('page', (currentPage + 1).toString());
    links.next = nextUrl.pathname + '?' + nextUrl.searchParams.toString();
  }
  
  return links;
};

// Breadcrumb için URL'ler
export const generateBreadcrumbUrls = (breadcrumbs) => {
  return breadcrumbs.map(item => ({
    ...item,
    url: normalizeUrl(item.url || item.path),
  }));
};

// Sitemap için URL'ler
export const generateSitemapUrls = (items, pageType) => {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://vineta.com';
  
  return items.map(item => {
    let url;
    
    switch (pageType) {
      case 'products':
        url = `${baseUrl}/products/${item.slug}`;
        break;
      case 'categories':
        url = `${baseUrl}/kategoriler/${item.slug}`;
        break;
      default:
        url = `${baseUrl}${item.url || item.path}`;
    }
    
    return {
      url: normalizeUrl(url),
      lastModified: item.updatedAt || item.createdAt || new Date().toISOString(),
      changeFrequency: item.changeFrequency || 'weekly',
      priority: item.priority || 0.7,
    };
  });
};
