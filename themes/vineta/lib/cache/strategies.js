/**
 * Cache strategies for better performance
 * Implements various caching mechanisms for API responses
 */

// In-memory cache for development/small scale
const memoryCache = new Map();

// Cache configuration
const CACHE_CONFIG = {
  // Product list cache - 30 minutes
  PRODUCT_LIST: {
    ttl: 30 * 60 * 1000, // 30 minutes
    key: 'product_list',
  },
  
  // Product detail cache - 1 hour
  PRODUCT_DETAIL: {
    ttl: 60 * 60 * 1000, // 1 hour
    key: 'product_detail',
  },
  
  // Filter options cache - 1 hour
  FILTER_OPTIONS: {
    ttl: 60 * 60 * 1000, // 1 hour
    key: 'filter_options',
  },
  
  // Category list cache - 2 hours
  CATEGORY_LIST: {
    ttl: 2 * 60 * 60 * 1000, // 2 hours
    key: 'category_list',
  },
  
  // Metadata cache - 4 hours
  METADATA: {
    ttl: 4 * 60 * 60 * 1000, // 4 hours
    key: 'metadata',
  },
};

/**
 * Generate cache key with parameters
 */
function generateCacheKey(baseKey, params = {}) {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|');
  
  return sortedParams ? `${baseKey}:${sortedParams}` : baseKey;
}

/**
 * Check if cache entry is valid
 */
function isCacheValid(entry) {
  if (!entry) return false;
  return Date.now() - entry.timestamp < entry.ttl;
}

/**
 * Get data from cache
 */
export function getFromCache(cacheType, params = {}) {
  try {
    const config = CACHE_CONFIG[cacheType];
    if (!config) return null;
    
    const cacheKey = generateCacheKey(config.key, params);
    const entry = memoryCache.get(cacheKey);
    
    if (isCacheValid(entry)) {
      console.log(`Cache HIT for ${cacheKey}`);
      return entry.data;
    } else if (entry) {
      // Remove expired entry
      memoryCache.delete(cacheKey);
      console.log(`Cache EXPIRED for ${cacheKey}`);
    }
    
    console.log(`Cache MISS for ${cacheKey}`);
    return null;
  } catch (error) {
    console.error('Error getting from cache:', error);
    return null;
  }
}

/**
 * Set data in cache
 */
export function setInCache(cacheType, data, params = {}) {
  try {
    const config = CACHE_CONFIG[cacheType];
    if (!config) return false;
    
    const cacheKey = generateCacheKey(config.key, params);
    const entry = {
      data,
      timestamp: Date.now(),
      ttl: config.ttl,
    };
    
    memoryCache.set(cacheKey, entry);
    console.log(`Cache SET for ${cacheKey}`);
    return true;
  } catch (error) {
    console.error('Error setting cache:', error);
    return false;
  }
}

/**
 * Clear specific cache entries
 */
export function clearCache(cacheType, params = {}) {
  try {
    const config = CACHE_CONFIG[cacheType];
    if (!config) return false;
    
    const cacheKey = generateCacheKey(config.key, params);
    const deleted = memoryCache.delete(cacheKey);
    
    if (deleted) {
      console.log(`Cache CLEARED for ${cacheKey}`);
    }
    
    return deleted;
  } catch (error) {
    console.error('Error clearing cache:', error);
    return false;
  }
}

/**
 * Clear all cache entries
 */
export function clearAllCache() {
  try {
    const size = memoryCache.size;
    memoryCache.clear();
    console.log(`All cache CLEARED (${size} entries)`);
    return true;
  } catch (error) {
    console.error('Error clearing all cache:', error);
    return false;
  }
}

/**
 * Get cache statistics
 */
export function getCacheStats() {
  const stats = {
    totalEntries: memoryCache.size,
    entries: [],
  };
  
  for (const [key, entry] of memoryCache.entries()) {
    stats.entries.push({
      key,
      isValid: isCacheValid(entry),
      age: Date.now() - entry.timestamp,
      ttl: entry.ttl,
    });
  }
  
  return stats;
}

/**
 * Cache wrapper for async functions
 */
export async function withCache(cacheType, params, fetchFunction) {
  // Try to get from cache first
  const cachedData = getFromCache(cacheType, params);
  if (cachedData) {
    return cachedData;
  }
  
  // Fetch fresh data
  try {
    const freshData = await fetchFunction();
    
    // Cache the result
    setInCache(cacheType, freshData, params);
    
    return freshData;
  } catch (error) {
    console.error('Error in withCache:', error);
    throw error;
  }
}

/**
 * Preload cache with common data
 */
export async function preloadCache() {
  try {
    console.log('Starting cache preload...');
    
    // This could preload common filter options, categories, etc.
    // Implementation depends on your specific needs
    
    console.log('Cache preload completed');
  } catch (error) {
    console.error('Error preloading cache:', error);
  }
}

// Cache cleanup interval (every 10 minutes)
if (typeof window === 'undefined') { // Server-side only
  setInterval(() => {
    const stats = getCacheStats();
    const expiredKeys = stats.entries
      .filter(entry => !entry.isValid)
      .map(entry => entry.key);
    
    expiredKeys.forEach(key => memoryCache.delete(key));
    
    if (expiredKeys.length > 0) {
      console.log(`Cache cleanup: removed ${expiredKeys.length} expired entries`);
    }
  }, 10 * 60 * 1000); // 10 minutes
}
