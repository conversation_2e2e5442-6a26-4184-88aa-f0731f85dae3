/**
 * Image utility functions for handling product images
 */

// Default placeholder image
export const DEFAULT_PLACEHOLDER = '/images/placeholder-product.svg';

/**
 * Get image source with fallback to placeholder
 * @param {string} imageSrc - Original image source
 * @param {string} fallback - Fallback image (optional)
 * @returns {string|null} Valid image source, placeholder, or null for empty strings
 */
export function getImageSrc(imageSrc, fallback = DEFAULT_PLACEHOLDER) {
  // Check if imageSrc is valid (not null, undefined, empty string, or just whitespace)
  if (!imageSrc || typeof imageSrc !== 'string' || imageSrc.trim() === '') {
    return fallback;
  }

  // If it's a relative path, ensure it starts with /
  if (!imageSrc.startsWith('http') && !imageSrc.startsWith('/')) {
    return `/${imageSrc}`;
  }

  return imageSrc;
}

/**
 * Get hover image source with fallback to main image or placeholder
 * @param {string} hoverSrc - Hover image source
 * @param {string} mainSrc - Main image source as fallback
 * @param {string} placeholder - Final fallback placeholder
 * @returns {string} Valid image source
 */
export function getHoverImageSrc(hoverSrc, mainSrc, placeholder = DEFAULT_PLACEHOLDER) {
  // First try hover image
  if (hoverSrc && typeof hoverSrc === 'string' && hoverSrc.trim() !== '') {
    return hoverSrc;
  }
  
  // Then try main image
  if (mainSrc && typeof mainSrc === 'string' && mainSrc.trim() !== '') {
    return mainSrc;
  }
  
  // Finally use placeholder
  return placeholder;
}

/**
 * Check if image source is valid
 * @param {string} imageSrc - Image source to check
 * @returns {boolean} True if valid, false otherwise
 */
export function isValidImageSrc(imageSrc) {
  return imageSrc && typeof imageSrc === 'string' && imageSrc.trim() !== '';
}
