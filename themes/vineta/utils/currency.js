/**
 * Currency utility functions for converting prices to Turkish Lira (TL)
 */

// Exchange rate from USD to TL (this should be fetched from an API in production)
const USD_TO_TL_RATE = 34.50; // Example rate, should be dynamic

/**
 * Convert USD price to Turkish Lira
 * @param {number} usdPrice - Price in USD
 * @param {number} exchangeRate - Exchange rate (optional, uses default if not provided)
 * @returns {number} Price in TL
 */
export function convertToTL(usdPrice, exchangeRate = USD_TO_TL_RATE) {
  if (typeof usdPrice !== 'number' || isNaN(usdPrice)) {
    return 0;
  }
  return usdPrice * exchangeRate;
}

/**
 * Format price in Turkish Lira with proper formatting
 * @param {number} price - Price in TL (already in Turkish Lira)
 * @param {boolean} convertFromUSD - Whether to convert from USD first (deprecated, kept for compatibility)
 * @param {number} exchangeRate - Exchange rate (optional, deprecated)
 * @returns {string} Formatted price string
 */
export function formatTLPrice(price, convertFromUSD = false, exchangeRate = USD_TO_TL_RATE) {
  if (typeof price !== 'number' || isNaN(price)) {
    return '₺0,00';
  }

  // Since prices are already in TL, we don't need to convert unless explicitly requested
  const tlPrice = convertFromUSD ? convertToTL(price, exchangeRate) : price;

  // Format with Turkish locale
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(tlPrice);
}

/**
 * Format price without currency symbol (just the number)
 * @param {number} price - Price in TL (already in Turkish Lira)
 * @param {boolean} convertFromUSD - Whether to convert from USD first (deprecated, kept for compatibility)
 * @param {number} exchangeRate - Exchange rate (optional, deprecated)
 * @returns {string} Formatted price number
 */
export function formatTLPriceNumber(price, convertFromUSD = false, exchangeRate = USD_TO_TL_RATE) {
  if (typeof price !== 'number' || isNaN(price)) {
    return '0,00';
  }

  // Since prices are already in TL, we don't need to convert unless explicitly requested
  const tlPrice = convertFromUSD ? convertToTL(price, exchangeRate) : price;

  // Format with Turkish locale without currency symbol
  return new Intl.NumberFormat('tr-TR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(tlPrice);
}

/**
 * Get current exchange rate (placeholder for API integration)
 * @returns {Promise<number>} Current USD to TL exchange rate
 */
export async function getCurrentExchangeRate() {
  // TODO: Implement API call to get real-time exchange rate
  // For now, return the default rate
  return USD_TO_TL_RATE;
}

/**
 * Update exchange rate globally
 * @param {number} newRate - New exchange rate
 */
export function updateExchangeRate(newRate) {
  if (typeof newRate === 'number' && newRate > 0) {
    USD_TO_TL_RATE = newRate;
  }
}

// Export the current rate for reference
export { USD_TO_TL_RATE };
