
import Checkout from "@/components/otherPages/Checkout";
import Link from "next/link";
import React from "react";


/**
 * @typedef {Object} AddressInfo
 * @property {string} firstName - Müşteri adı
 * @property {string} lastName - Müşteri soyadı
 * @property {string} country - Ülke
 * @property {string} address - Açık adres
 * @property {string} [apartment] - Apartman/Suite bilgisi (opsiyonel)
 * @property {string} city - Şehir
 * @property {string} state - Eyalet/Bölge
 * @property {string} zipCode - Posta kodu
 * @property {string} phone - Telefon numarası
 */

/**
 * @typedef {Object} ContactInfo
 * @property {string} email - E-posta adresi
 * @property {string} phoneNumber - Telefon numarası
 */

/**
 * @typedef {Object} ShippingMethod
 * @property {string} id - Kargo yöntemi ID
 * @property {string} name - Kargo yöntemi adı
 * @property {number} price - Kargo ücreti
 * @property {string} estimatedDate - Tahmini teslimat tarihi
 * @property {boolean} isSelected - Seçili mi?
 */

/**
 * @typedef {Object} PaymentDetails
 * @property {string} method - Ödeme yöntemi (credit-card|bank-transfer|cash-on-delivery|paypal)
 * @property {Object} [cardInfo] - Kredi kartı bilgileri (method credit-card ise)
 * @property {string} cardInfo.cardNumber - Kart numarası
 * @property {string} cardInfo.expiryDate - Son kullanma tarihi
 * @property {string} cardInfo.securityCode - Güvenlik kodu
 * @property {string} cardInfo.nameOnCard - Kart üzerindeki isim
 * @property {boolean} cardInfo.useShippingAddress - Fatura adresi teslimat adresi ile aynı mı?
 */

/**
 * @typedef {Object} OrderSummary
 * @property {number} subtotal - Ara toplam
 * @property {number} discount - İndirim tutarı
 * @property {number} shipping - Kargo ücreti
 * @property {number} tax - Vergi tutarı
 * @property {number} total - Genel toplam
 */

/**
 * @typedef {Object} CheckoutFormData
 * @property {AddressInfo} addressInfo - Teslimat adresi bilgileri
 * @property {ContactInfo} contactInfo - İletişim bilgileri
 * @property {ShippingMethod} shippingMethod - Seçilen kargo yöntemi
 * @property {PaymentDetails} paymentDetails - Ödeme detayları
 * @property {OrderSummary} orderSummary - Sipariş özeti
 */

/**
 * Siparişi oluştur
 * @param {CheckoutFormData} checkoutData - Sipariş form verileri
 * @returns {Promise<{orderId: string, status: string, message: string}>}
 */
export const createOrder = async (checkoutData) => {
  return api.post('/cart', checkoutData);
};

/**
 * Kargo yöntemlerini getir
 * @returns {Promise<ShippingMethod[]>}
 */
export const getShippingMethods = async () => {
  return api.get('/shipping-methods');
};




export const metadata = {
  title: "Satın Al || Future Cosmetics",
  description: "Future Cosmetics, İyzico ile Güvenli alışveriş sunar.",
};
export default function page() {
  return (
    <>
      <>
        {/* Breadcrumb */}
        <div className="tf-breadcrumb">
          <div className="container">
            <ul className="breadcrumb-list">
              <li className="item-breadcrumb">
                <Link href={`/`} className="text">
                  Anasayfa
                </Link>
              </li>
              <li className="item-breadcrumb dot">
                <span />
              </li>
              <li className="item-breadcrumb">
                <span className="text">Satın Al</span>
              </li>
            </ul>
          </div>
        </div>
        {/* /Breadcrumb */}
        {/* Title Page */}
        <section className="page-title">
          <div className="container">
            <div className="box-title text-center justify-items-center">
              <h4 className="title">Satın Al</h4>
            </div>
          </div>
        </section>
        {/* /Title Page */}
      </>
      <Checkout />
    </>
  );
}
