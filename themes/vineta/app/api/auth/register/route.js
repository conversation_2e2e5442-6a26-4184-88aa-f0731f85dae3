import { NextResponse } from 'next/server'

export async function POST(request) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const { nameSurname, email, password, phoneNumber, taxOrIdentityNumber, taxOffice } = body
    
    if (!nameSurname || !email || !password) {
      return NextResponse.json(
        { 
          isSuccessful: false, 
          message: '<PERSON> Soyad, Email ve Şifre alanları zorunludur.' 
        },
        { status: 400 }
      )
    }

    // Call backend register API
    const response = await fetch('http://localhost:33800/customer/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        nameSurname,
        email,
        password,
        phoneNumber: phoneNumber || null,
        taxOrIdentityNumber: taxOrIdentityNumber || null,
        taxOffice: taxOffice || null,
      }),
    })

    const result = await response.json()

    if (response.ok && result.success && result.data?.isSuccessful) {
      return NextResponse.json({
        isSuccessful: true,
        message: result.data.message || 'Kayıt başarılı',
        customer: result.data.customer,
        token: result.data.token
      })
    } else {
      return NextResponse.json(
        {
          isSuccessful: false,
          message: result.data?.message || result.message || 'Kayıt sırasında bir hata oluştu'
        },
        { status: response.status || 400 }
      )
    }
  } catch (error) {
    console.error('Customer register error:', error)
    return NextResponse.json(
      {
        isSuccessful: false,
        message: 'Kayıt sırasında bir hata oluştu'
      },
      { status: 500 }
    )
  }
}
