import Breadcumb from "@/components/products/Breadcumb";
import Features from "@/components/products/Features";
import Products2 from "@/components/products/Products2";
import ErrorRetryButton from "@/components/common/ErrorRetryButton";
import { getProductListSSR, getProductListFiltersSSR } from "@/services/productList";
import { Suspense } from "react";
import React from "react";

// Client Component for error retry button - separate file needed
// This will be moved to a separate component file

// Cache configuration for better performance
export const revalidate = 1800; // Revalidate every 30 minutes
export const dynamic = 'force-dynamic'; // Allow dynamic content for filters


/*
export const createSearchParams = ({
  page,
  limit,
  sort,
  categories,
  brands,
  sizes,
  priceRange,
  inStock,
}: {
  page?: number;
  limit?: number;
  sort?: { field: string; direction: string };
  categories?: string[]; //category slugs
  brands?: number[];
  sizes?: string[];
  priceRange?: { minPrice: number; maxPrice: number };
  inStock?: boolean;
}) => {
  const params = new URLSearchParams();

  if (page) params.append('page', page.toString());
  if (limit) params.append('limit', limit.toString());
  if (sort) params.append('sort', `${sort.field}:${sort.direction}`);
  if (categories?.length) params.append('category', categories.join(','));
  if (brands?.length) params.append('brand', brands.join(','));
  if (sizes?.length) params.append('size', sizes.join(','));
  if (priceRange?.minPrice) params.append('price_min', priceRange.minPrice.toString());
  if (priceRange?.maxPrice) params.append('price_max', priceRange.maxPrice.toString());
  if (inStock !== undefined) params.append('in_stock', inStock.toString());

  return params.toString();
};
*/

// query and url example : /products?category=cleaners,creams&brand=5,8&size=50ml,100ml&price_min=100&price_max=500&in_stock=true&page=1&limit=12&sort=price:desc


// Optimized server-side data fetching with performance monitoring
async function getPageData(searchParams) {
  const startTime = Date.now();

  try {
    // Parse variant attributes from search params
    const variantAttributes = {};
    Object.entries(searchParams).forEach(([key, value]) => {
      if (key.startsWith('attr_')) {
        const attrName = key.replace('attr_', '');
        variantAttributes[attrName] = value.split(',').filter(Boolean);
      }
    });

    // Build filter parameters from URL search params
    const filters = {
      page: parseInt(searchParams.page) || 1,
      limit: parseInt(searchParams.limit) || 12,
      sort: searchParams.sort || 'default',
      categories: searchParams.categories?.split(',').filter(Boolean) || [],
      //brands: searchParams.brands?.split(',').filter(Boolean) || [],
      //sizes: searchParams.sizes?.split(',').filter(Boolean) || [],
      variantAttributes: Object.keys(variantAttributes).length > 0 ? variantAttributes : undefined,
      priceMin: parseFloat(searchParams.price_min) || null,
      priceMax: parseFloat(searchParams.price_max) || null,
      inStock: searchParams.in_stock === 'true' ? true : searchParams.in_stock === 'false' ? false : null,
      search: searchParams.search || '',
      pageType: 'products'
    };

    // Parallel data fetching for better performance
    const [productsResponse, filtersResponse] = await Promise.all([
      getProductListSSR(filters),
      getProductListFiltersSSR('products')
    ]);

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Products page data fetched in ${Date.now() - startTime}ms`);
      console.log('Products Response Success:', productsResponse?.success);
      console.log('Products Response Data Structure:', {
        hasData: !!productsResponse?.data,
        hasProducts: !!productsResponse?.data?.products,
        productsLength: productsResponse?.data?.products?.length,
        hasPagination: !!productsResponse?.data?.pagination
      });
      console.log('Filters Response Success:', filtersResponse?.success);
    }

    // Check if we have valid data
    const hasValidProducts = productsResponse?.success && productsResponse?.data?.products && Array.isArray(productsResponse.data.products);
    const hasValidFilters = filtersResponse?.success && filtersResponse?.data;

    if (process.env.NODE_ENV === 'development') {
      console.log('Final validation:', {
        hasValidProducts,
        hasValidFilters,
        productsCount: productsResponse?.data?.products?.length || 0,
        willShowError: !hasValidProducts
      });
    }

    return {
      initialData: {
        products: hasValidProducts ? productsResponse.data.products : [],
        pagination: productsResponse?.success ? productsResponse.data?.pagination || {} : {}
      },
      initialFilters: hasValidFilters ? filtersResponse.data : {},
      error: !hasValidProducts ? (productsResponse?.message || 'Ürünler yüklenirken bir hata oluştu.') : null,
      fetchTime: Date.now() - startTime
    };
  } catch (error) {
    console.error('Error fetching page data:', error);
    return {
      initialData: { products: [], pagination: {} },
      initialFilters: {},
      error: 'Ürünler yüklenirken bir hata oluştu.',
      fetchTime: Date.now() - startTime
    };
  }
}

export async function generateMetadata({ searchParams }) {
  // Dynamic metadata based on search parameters
  const resolvedSearchParams = await searchParams;
  const hasFilters = resolvedSearchParams.categories || resolvedSearchParams.brands || resolvedSearchParams.search;
  const searchTerm = resolvedSearchParams.search;
  const category = resolvedSearchParams.categories?.split(',')[0];

  let title = "Tüm Ürünler | B2C Mağaza";
  let description = "Geniş ürün yelpazemizi keşfedin. Kaliteli ürünler, uygun fiyatlar ve hızlı teslimat.";

  if (searchTerm) {
    title = `"${searchTerm}" Arama Sonuçları | B2C Mağaza`;
    description = `"${searchTerm}" için arama sonuçları. En uygun fiyatlarla kaliteli ürünler.`;
  } else if (category) {
    title = `${category} Kategorisi | B2C Mağaza`;
    description = `${category} kategorisindeki ürünleri keşfedin. Kaliteli ürünler, uygun fiyatlar.`;
  } else if (hasFilters) {
    title = "Filtrelenmiş Ürünler | B2C Mağaza";
    description = "Seçtiğiniz kriterlere uygun ürünleri keşfedin.";
  }

  return {
    title,
    description,
    keywords: "ürünler, alışveriş, online mağaza, kaliteli ürünler",
    alternates: {
      canonical: "/products",
    },
    openGraph: {
      title,
      description,
      type: "website",
      url: "/products",
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
    },
    robots: "index, follow",
  };
}

// Enhanced skeleton component for products page
function ProductsPageSkeleton() {
  return (
    <div className="container">
      <div className="animate-pulse">
        {/* Breadcrumb skeleton */}
        <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>

        {/* Filter and sort controls skeleton */}
        <div className="flex justify-between items-center mb-6">
          <div className="h-6 bg-gray-200 rounded w-32"></div>
          <div className="h-10 bg-gray-200 rounded w-48"></div>
        </div>

        {/* Products grid skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {[...Array(12)].map((_, i) => (
            <div key={i} className="space-y-3">
              <div className="h-64 bg-gray-200 rounded-lg"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            </div>
          ))}
        </div>

        {/* Pagination skeleton */}
        <div className="flex justify-center mt-8">
          <div className="flex space-x-2">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-10 w-10 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default async function ProductsPage({ searchParams }) {
  const startTime = Date.now();
  const resolvedSearchParams = await searchParams;
  const { initialData, initialFilters, error, fetchTime } = await getPageData(resolvedSearchParams);

  // Log total page generation time in development
  if (process.env.NODE_ENV === 'development') {
    console.log(`Products page generated in ${Date.now() - startTime}ms (data fetch: ${fetchTime}ms)`);
  }

  return (
    <>
      <Breadcumb
        title="Tüm Ürünler"
        description="Geniş ürün yelpazemizi keşfedin"
        crumbs={[{ label: "Tüm Ürünler", path: "/products" }]}
        fullWidth={true}
      />

      {error ? (
        <div className="container">
          <div className="alert alert-danger" role="alert">
            {error}
            <ErrorRetryButton />
          </div>
        </div>
      ) : (
        <Suspense fallback={<ProductsPageSkeleton />}>
          <Products2
            initialData={initialData}
            initialFilters={initialFilters}
            pageType="products"
            initialLayout={3}
          />
        </Suspense>
      )}

      {/* <Features /> */}
    </>
  );
}
